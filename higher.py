import yt_dlp

def download_instagram_hq(url):
    ydl_opts = {
        'format': 'bestvideo+bestaudio/best',
        'merge_output_format': 'mp4',
        'outtmpl': 'reels_hq/%(id)s.%(ext)s',
        'overwrites': True,
        'socket_timeout': 60,  # increase timeout
        'retries': 10,         # retry more times
        'fragment_retries': 10 # retry per fragment
    }
    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        ydl.download([url])

download_instagram_hq("https://www.instagram.com/reel/CRQgLAjFDuM/")
