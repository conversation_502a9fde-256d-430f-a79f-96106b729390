# Universal Video Downloader - UV Commands

.PHONY: help install run dev clean test format lint

help: ## Show this help message
	@echo "Universal Video Downloader - Available Commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'

install: ## Install dependencies using UV
	@echo "📦 Installing dependencies with UV..."
	uv sync

run: ## Run the application
	@echo "🚀 Starting Universal Video Downloader..."
	uv run python app.py

dev: ## Run in development mode with auto-reload
	@echo "🔧 Starting in development mode..."
	uv run python app.py --debug

clean: ## Clean up virtual environment and cache
	@echo "🧹 Cleaning up..."
	rm -rf .venv
	rm -rf __pycache__
	rm -rf *.pyc
	rm -rf .pytest_cache
	find . -type d -name "__pycache__" -delete
	find . -type f -name "*.pyc" -delete

clean-downloads: ## Clean up corrupted download files
	@echo "🧹 Cleaning up corrupted downloads..."
	uv run python cleanup_downloads.py

test: ## Run tests (if available)
	@echo "🧪 Running tests..."
	uv run pytest

format: ## Format code with black
	@echo "🎨 Formatting code..."
	uv run black .

lint: ## Lint code with flake8
	@echo "🔍 Linting code..."
	uv run flake8 .

setup: ## Initial setup (install UV if needed and setup project)
	@echo "🔧 Setting up project..."
	@if ! command -v uv &> /dev/null; then \
		echo "❌ UV not found. Please install UV first:"; \
		echo "   curl -LsSf https://astral.sh/uv/install.sh | sh"; \
		exit 1; \
	fi
	uv sync
	@echo "✅ Setup complete! Run 'make run' to start the application."
	@echo "📱 The app will be available at: http://localhost:8080"

update: ## Update dependencies
	@echo "⬆️ Updating dependencies..."
	uv sync --upgrade

info: ## Show project information
	@echo "📋 Project Information:"
	@echo "  Name: Universal Video Downloader"
	@echo "  Python: $(shell uv run python --version)"
	@echo "  UV: $(shell uv --version)"
	@echo "  Dependencies: $(shell uv pip list | wc -l) packages"

# Default target
.DEFAULT_GOAL := help
