{% extends "base.html" %}

{% block title %}Universal Video Downloader - Download from Any Platform{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <div class="hero-content">
                    <h1 class="hero-title">
                        <i class="fas fa-download text-primary"></i>
                        Universal Video Downloader
                    </h1>
                    <p class="hero-subtitle">
                        Download videos from Instagram, Facebook, TikTok, YouTube, and more!
                        Fast, free, and easy to use.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Download Form Section -->
<section class="download-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="download-card">
                    <div class="card-body p-5">
                        <form id="downloadForm">
                            <div class="mb-4">
                                <label for="videoUrl" class="form-label fw-bold">
                                    <i class="fas fa-link me-2"></i>Video URL
                                </label>
                                <div class="input-group input-group-lg">
                                    <span class="input-group-text bg-primary text-white">
                                        <i class="fas fa-paste"></i>
                                    </span>
                                    <input 
                                        type="url" 
                                        class="form-control" 
                                        id="videoUrl" 
                                        placeholder="Paste your video URL here (Instagram, Facebook, TikTok, YouTube, etc.)"
                                        required
                                    >
                                </div>
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Supports links from Instagram, Facebook, TikTok, YouTube, Twitter, and many more platforms
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg download-btn">
                                    <i class="fas fa-download me-2"></i>
                                    <span class="btn-text">Start Download</span>
                                    <div class="spinner-border spinner-border-sm ms-2 d-none" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title">Why Choose Our Downloader?</h2>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h4>Lightning Fast</h4>
                    <p>High-speed downloads with real-time progress tracking</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4>Safe & Secure</h4>
                    <p>No malware, no ads, completely safe to use</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="feature-card text-center">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4>All Devices</h4>
                    <p>Works on desktop, tablet, and mobile devices</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Platform Support Section -->
<section class="platforms-section">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="section-title">Supported Platforms</h2>
                <p class="section-subtitle">Download from your favorite social media platforms</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-6 col-md-3 col-lg-2">
                <div class="platform-card">
                    <i class="fab fa-instagram"></i>
                    <span>Instagram</span>
                </div>
            </div>
            <div class="col-6 col-md-3 col-lg-2">
                <div class="platform-card">
                    <i class="fab fa-facebook"></i>
                    <span>Facebook</span>
                </div>
            </div>
            <div class="col-6 col-md-3 col-lg-2">
                <div class="platform-card">
                    <i class="fab fa-tiktok"></i>
                    <span>TikTok</span>
                </div>
            </div>
            <div class="col-6 col-md-3 col-lg-2">
                <div class="platform-card">
                    <i class="fab fa-youtube"></i>
                    <span>YouTube</span>
                </div>
            </div>
            <div class="col-6 col-md-3 col-lg-2">
                <div class="platform-card">
                    <i class="fab fa-twitter"></i>
                    <span>Twitter</span>
                </div>
            </div>
            <div class="col-6 col-md-3 col-lg-2">
                <div class="platform-card">
                    <i class="fas fa-plus"></i>
                    <span>Many More</span>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_scripts %}
<script>
document.getElementById('downloadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const url = document.getElementById('videoUrl').value.trim();
    const btn = document.querySelector('.download-btn');
    const btnText = btn.querySelector('.btn-text');
    const spinner = btn.querySelector('.spinner-border');
    
    if (!url) {
        alert('Please enter a valid URL');
        return;
    }
    
    // Show loading state
    btn.disabled = true;
    btnText.textContent = 'Starting Download...';
    spinner.classList.remove('d-none');
    
    // Send download request
    fetch('/download', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: url })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }
        // Redirect to download page
        window.location.href = `/download-page/${data.download_id}`;
    })
    .catch(error => {
        alert('Error: ' + error.message);
        // Reset button state
        btn.disabled = false;
        btnText.textContent = 'Start Download';
        spinner.classList.add('d-none');
    });
});
</script>
{% endblock %}
