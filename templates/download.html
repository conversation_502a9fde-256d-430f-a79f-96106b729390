{% extends "base.html" %}

{% block title %}Downloading Video - Universal Video Downloader{% endblock %}

{% block content %}
<!-- Download Progress Section -->
<section class="download-progress-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="progress-card">
                    <div class="card-body p-5">
                        <!-- Video Info -->
                        <div id="videoInfo" class="video-info mb-4" style="display: none;">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h4 id="videoTitle" class="mb-2">Loading video information...</h4>
                                    <p class="text-muted mb-1">
                                        <i class="fas fa-user me-2"></i>
                                        <span id="videoUploader">Unknown</span>
                                    </p>
                                    <p class="text-muted mb-0">
                                        <i class="fas fa-clock me-2"></i>
                                        <span id="videoDuration">--:--</span>
                                    </p>
                                </div>
                                <div class="col-md-4 text-end">
                                    <div class="status-badge" id="statusBadge">
                                        <i class="fas fa-spinner fa-spin me-2"></i>
                                        <span id="statusText">Preparing...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="progress-container mb-4">
                            <div class="progress-header d-flex justify-content-between mb-2">
                                <span class="progress-label">Download Progress</span>
                                <span class="progress-percentage" id="progressPercentage">0%</span>
                            </div>
                            <div class="progress progress-lg">
                                <div 
                                    class="progress-bar progress-bar-striped progress-bar-animated" 
                                    id="progressBar"
                                    role="progressbar" 
                                    style="width: 0%"
                                    aria-valuenow="0" 
                                    aria-valuemin="0" 
                                    aria-valuemax="100"
                                ></div>
                            </div>
                        </div>

                        <!-- Download Stats -->
                        <div class="download-stats">
                            <div class="row text-center">
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <i class="fas fa-download text-primary"></i>
                                        <div class="stat-value" id="downloadedSize">0 MB</div>
                                        <div class="stat-label">Downloaded</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <i class="fas fa-tachometer-alt text-success"></i>
                                        <div class="stat-value" id="downloadSpeed">0 MB/s</div>
                                        <div class="stat-label">Speed</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="stat-item">
                                        <i class="fas fa-clock text-info"></i>
                                        <div class="stat-value" id="timeRemaining">--:--</div>
                                        <div class="stat-label">Time Left</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Download Complete Section -->
                        <div id="downloadComplete" class="download-complete text-center" style="display: none;">
                            <div class="success-icon mb-3">
                                <i class="fas fa-check-circle text-success"></i>
                            </div>
                            <h3 class="text-success mb-3">Download Complete!</h3>
                            <p class="mb-4">Your video has been successfully downloaded.</p>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <button class="btn btn-success btn-lg" id="downloadFileBtn">
                                    <i class="fas fa-download me-2"></i>
                                    Download File
                                </button>
                                <a href="{{ url_for('index') }}" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-plus me-2"></i>
                                    Download Another
                                </a>
                            </div>
                        </div>

                        <!-- Error Section -->
                        <div id="downloadError" class="download-error text-center" style="display: none;">
                            <div class="error-icon mb-3">
                                <i class="fas fa-exclamation-triangle text-danger"></i>
                            </div>
                            <h3 class="text-danger mb-3">Download Failed</h3>
                            <p class="mb-4" id="errorMessage">An error occurred during download.</p>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                <button class="btn btn-danger btn-lg" onclick="location.reload()">
                                    <i class="fas fa-redo me-2"></i>
                                    Try Again
                                </button>
                                <a href="{{ url_for('index') }}" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-home me-2"></i>
                                    Go Home
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Tips Section -->
<section class="tips-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="tips-card">
                    <h5 class="mb-3">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        Download Tips
                    </h5>
                    <ul class="tips-list">
                        <li><i class="fas fa-check text-success me-2"></i>Keep this page open until download completes</li>
                        <li><i class="fas fa-check text-success me-2"></i>Large videos may take longer to process</li>
                        <li><i class="fas fa-check text-success me-2"></i>Download will start automatically once processing is complete</li>
                        <li><i class="fas fa-check text-success me-2"></i>Check your browser's download folder for the file</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_scripts %}
<script>
const downloadId = '{{ download_id }}';
const socket = io();

// Join the download room
socket.emit('join_download', { download_id: downloadId });

// Format bytes to human readable
function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// Format seconds to MM:SS
function formatTime(seconds) {
    if (!seconds || seconds === Infinity) return '--:--';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

// Format duration from seconds
function formatDuration(seconds) {
    if (!seconds) return '--:--';
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${mins}:${secs.toString().padStart(2, '0')}`;
}

// Update progress display
function updateProgress(data) {
    const progressBar = document.getElementById('progressBar');
    const progressPercentage = document.getElementById('progressPercentage');
    const downloadedSize = document.getElementById('downloadedSize');
    const downloadSpeed = document.getElementById('downloadSpeed');
    const timeRemaining = document.getElementById('timeRemaining');
    const statusText = document.getElementById('statusText');
    const statusBadge = document.getElementById('statusBadge');
    
    // Update progress bar
    const percent = data.percent || 0;
    progressBar.style.width = percent + '%';
    progressBar.setAttribute('aria-valuenow', percent);
    progressPercentage.textContent = percent + '%';
    
    // Update stats
    if (data.downloaded) {
        downloadedSize.textContent = formatBytes(data.downloaded);
    }
    
    if (data.speed) {
        downloadSpeed.textContent = formatBytes(data.speed) + '/s';
    }
    
    if (data.eta) {
        timeRemaining.textContent = formatTime(data.eta);
    }
    
    // Update status
    if (data.status === 'downloading') {
        statusText.textContent = 'Downloading...';
        statusBadge.innerHTML = '<i class="fas fa-download me-2"></i><span>Downloading...</span>';
        statusBadge.className = 'status-badge status-downloading';
    }
    
    // Update video info if available
    if (data.title) {
        document.getElementById('videoTitle').textContent = data.title;
        document.getElementById('videoInfo').style.display = 'block';
    }
    
    if (data.uploader) {
        document.getElementById('videoUploader').textContent = data.uploader;
    }
    
    if (data.duration) {
        document.getElementById('videoDuration').textContent = formatDuration(data.duration);
    }
}

// Handle progress updates
socket.on('progress_update', function(data) {
    updateProgress(data);
});

// Handle download completion
socket.on('download_complete', function(data) {
    const statusBadge = document.getElementById('statusBadge');
    const downloadComplete = document.getElementById('downloadComplete');
    
    // Update status
    statusBadge.innerHTML = '<i class="fas fa-check-circle me-2"></i><span>Complete</span>';
    statusBadge.className = 'status-badge status-complete';
    
    // Update progress to 100%
    updateProgress({ percent: 100 });
    
    // Show completion section
    downloadComplete.style.display = 'block';
    
    // Set up download button (you'll need to implement file serving)
    const downloadBtn = document.getElementById('downloadFileBtn');
    if (data.filename) {
        downloadBtn.onclick = function() {
            // Implement file download logic
            window.location.href = `/download-file/${downloadId}`;
        };
    }
});

// Handle download errors
socket.on('download_error', function(data) {
    const statusBadge = document.getElementById('statusBadge');
    const downloadError = document.getElementById('downloadError');
    const errorMessage = document.getElementById('errorMessage');
    
    // Update status
    statusBadge.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i><span>Error</span>';
    statusBadge.className = 'status-badge status-error';
    
    // Show error section
    downloadError.style.display = 'block';
    errorMessage.textContent = data.error || 'An unknown error occurred.';
});

// Initial progress check
fetch(`/progress/${downloadId}`)
    .then(response => response.json())
    .then(data => {
        if (data.status !== 'not_found') {
            updateProgress(data);
        }
    })
    .catch(console.error);
</script>
{% endblock %}
