from flask import Flask, render_template, request, jsonify, send_file, session
from flask_socketio import Socket<PERSON>, emit
import yt_dlp
import os
import threading
import uuid
import json
from datetime import datetime
import time
import socket

def find_free_port(start_port=8080):
    """Find a free port starting from the given port"""
    port = start_port
    while port < start_port + 100:  # Try up to 100 ports
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            port += 1
    return start_port  # Fallback to original port

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# Add CORS headers to all responses
@app.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

# Create downloads directory if it doesn't exist
DOWNLOAD_DIR = 'downloads'
if not os.path.exists(DOWNLOAD_DIR):
    os.makedirs(DOWNLOAD_DIR)

# Store download progress
download_progress = {}

class ProgressHook:
    def __init__(self, download_id, socketio_instance):
        self.download_id = download_id
        self.socketio = socketio_instance
        
    def __call__(self, d):
        if d['status'] == 'downloading':
            try:
                # Calculate percentage
                if 'total_bytes' in d and d['total_bytes']:
                    percent = (d['downloaded_bytes'] / d['total_bytes']) * 100
                elif 'total_bytes_estimate' in d and d['total_bytes_estimate']:
                    percent = (d['downloaded_bytes'] / d['total_bytes_estimate']) * 100
                else:
                    percent = 0
                
                # Get speed and ETA
                speed = d.get('speed', 0)
                eta = d.get('eta', 0)
                
                # Update progress
                progress_data = {
                    'percent': round(percent, 1),
                    'downloaded': d.get('downloaded_bytes', 0),
                    'total': d.get('total_bytes', d.get('total_bytes_estimate', 0)),
                    'speed': speed,
                    'eta': eta,
                    'status': 'downloading'
                }
                
                download_progress[self.download_id] = progress_data
                
                # Emit to specific download session
                self.socketio.emit('progress_update', progress_data, room=self.download_id)
                
            except Exception as e:
                print(f"Progress hook error: {e}")
                
        elif d['status'] == 'finished':
            progress_data = {
                'percent': 100,
                'status': 'finished',
                'filename': d.get('filename', '')
            }
            download_progress[self.download_id] = progress_data
            self.socketio.emit('download_complete', progress_data, room=self.download_id)

def check_ffmpeg():
    """Check if FFmpeg is available"""
    import shutil
    return shutil.which('ffmpeg') is not None

def download_video(url, download_id):
    """Download video using yt-dlp with progress tracking"""
    try:
        # Check if FFmpeg is available for merging
        has_ffmpeg = check_ffmpeg()

        # Configure yt-dlp options with better error handling and proper merging
        ydl_opts = {
            'outtmpl': f'{DOWNLOAD_DIR}/%(uploader)s - %(title)s.%(ext)s',
            'overwrites': True,
            'socket_timeout': 60,
            'retries': 10,
            'fragment_retries': 10,
            'progress_hooks': [ProgressHook(download_id, socketio)],
            # Add headers to avoid 403 errors
            'http_headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            },
            # Skip unavailable fragments
            'ignoreerrors': False,
            'no_warnings': False,
            # Force filename sanitization
            'restrictfilenames': True,
        }

        if has_ffmpeg:
            # If FFmpeg is available, use best quality with merging
            ydl_opts.update({
                'format': 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/bestvideo+bestaudio/best[ext=mp4]/best',
                'merge_output_format': 'mp4',
                'postprocessors': [{
                    'key': 'FFmpegVideoConvertor',
                    'preferedformat': 'mp4',
                }],
                'keepvideo': False,
            })
            print("🎬 Using FFmpeg for high-quality video merging")
        else:
            # If no FFmpeg, use single file formats only
            ydl_opts.update({
                'format': 'best[ext=mp4]/best[height<=720]/mp4/best',
                'prefer_free_formats': True,
            })
            print("⚠️ FFmpeg not found, using single-file formats (may be lower quality)")
            print("💡 Install FFmpeg for better quality: brew install ffmpeg (macOS) or apt install ffmpeg (Linux)")
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Get video info first
            info = ydl.extract_info(url, download=False)
            title = info.get('title', 'Unknown')
            duration = info.get('duration', 0)
            uploader = info.get('uploader', 'Unknown')
            
            # Store video info
            download_progress[download_id].update({
                'title': title,
                'duration': duration,
                'uploader': uploader
            })
            
            # Start download
            ydl.download([url])
            
    except Exception as e:
        error_data = {
            'status': 'error',
            'error': str(e)
        }
        download_progress[download_id] = error_data
        socketio.emit('download_error', error_data, room=download_id)

@app.route('/')
def index():
    """Main page with video URL input"""
    return render_template('index.html')

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'Universal Video Downloader is running',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/download', methods=['POST', 'OPTIONS'])
def start_download():
    """Start video download process"""
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        return '', 200

    try:
        # Get JSON data with error handling
        if not request.is_json:
            return jsonify({'error': 'Request must be JSON'}), 400

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No JSON data provided'}), 400

        url = data.get('url', '').strip()

        if not url:
            return jsonify({'error': 'URL is required'}), 400

        # Basic URL validation
        if not (url.startswith('http://') or url.startswith('https://')):
            return jsonify({'error': 'URL must start with http:// or https://'}), 400

        print(f"📥 Received download request for: {url}")

        # Generate unique download ID
        download_id = str(uuid.uuid4())

        # Initialize progress
        download_progress[download_id] = {
            'percent': 0,
            'status': 'starting',
            'url': url,
            'created_at': datetime.now().isoformat()
        }

        # Start download in background thread
        thread = threading.Thread(target=download_video, args=(url, download_id))
        thread.daemon = True
        thread.start()

        print(f"✅ Started download with ID: {download_id}")
        return jsonify({'download_id': download_id})

    except Exception as e:
        print(f"❌ Error in start_download: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/progress/<download_id>')
def get_progress(download_id):
    """Get current download progress"""
    progress = download_progress.get(download_id, {'status': 'not_found'})
    return jsonify(progress)

@app.route('/download-page/<download_id>')
def download_page(download_id):
    """Download progress and completion page"""
    return render_template('download.html', download_id=download_id)

@app.route('/download-file/<download_id>')
def download_file(download_id):
    """Serve the downloaded file"""
    if download_id not in download_progress:
        return jsonify({'error': 'Download not found'}), 404

    progress = download_progress[download_id]
    if progress.get('status') != 'finished':
        return jsonify({'error': 'Download not completed'}), 400

    filename = progress.get('filename')
    if not filename or not os.path.exists(filename):
        return jsonify({'error': 'File not found'}), 404

    # Get the base filename for download
    base_filename = os.path.basename(filename)

    try:
        return send_file(
            filename,
            as_attachment=True,
            download_name=base_filename,
            mimetype='video/mp4'
        )
    except Exception as e:
        return jsonify({'error': f'Error serving file: {str(e)}'}), 500

@app.route('/api/supported-platforms')
def supported_platforms():
    """Get list of supported platforms"""
    platforms = [
        {'name': 'Instagram', 'icon': 'fab fa-instagram', 'domains': ['instagram.com']},
        {'name': 'Facebook', 'icon': 'fab fa-facebook', 'domains': ['facebook.com', 'fb.watch']},
        {'name': 'TikTok', 'icon': 'fab fa-tiktok', 'domains': ['tiktok.com']},
        {'name': 'YouTube', 'icon': 'fab fa-youtube', 'domains': ['youtube.com', 'youtu.be']},
        {'name': 'Twitter/X', 'icon': 'fab fa-twitter', 'domains': ['twitter.com', 'x.com']},
        {'name': 'Reddit', 'icon': 'fab fa-reddit', 'domains': ['reddit.com']},
        {'name': 'Vimeo', 'icon': 'fab fa-vimeo', 'domains': ['vimeo.com']},
        {'name': 'Dailymotion', 'icon': 'fas fa-video', 'domains': ['dailymotion.com']},
    ]
    return jsonify(platforms)

@socketio.on('join_download')
def on_join_download(data):
    """Join a specific download room for real-time updates"""
    download_id = data['download_id']
    session['download_id'] = download_id
    # Join the room for this specific download
    from flask_socketio import join_room
    join_room(download_id)

    # Send current progress if available
    if download_id in download_progress:
        emit('progress_update', download_progress[download_id])

@socketio.on('disconnect')
def on_disconnect():
    """Handle client disconnect"""
    print('Client disconnected')

if __name__ == '__main__':
    # Find an available port
    port = find_free_port(8080)
    print(f"🚀 Starting Universal Video Downloader on port {port}")
    print(f"📱 Open your browser and go to: http://localhost:{port}")
    print("⏹️  Press Ctrl+C to stop the server")
    print("=" * 50)

    try:
        socketio.run(app, debug=True, host='0.0.0.0', port=port)
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("💡 Try running on a different port or check if another service is using the port")
