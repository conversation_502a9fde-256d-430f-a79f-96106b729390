from flask import Flask, render_template, request, jsonify, send_file, session
from flask_socketio import Socket<PERSON>, emit
import yt_dlp
import os
import threading
import uuid
import json
from datetime import datetime
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
socketio = SocketIO(app, cors_allowed_origins="*")

# Create downloads directory if it doesn't exist
DOWNLOAD_DIR = 'downloads'
if not os.path.exists(DOWNLOAD_DIR):
    os.makedirs(DOWNLOAD_DIR)

# Store download progress
download_progress = {}

class ProgressHook:
    def __init__(self, download_id, socketio_instance):
        self.download_id = download_id
        self.socketio = socketio_instance
        
    def __call__(self, d):
        if d['status'] == 'downloading':
            try:
                # Calculate percentage
                if 'total_bytes' in d and d['total_bytes']:
                    percent = (d['downloaded_bytes'] / d['total_bytes']) * 100
                elif 'total_bytes_estimate' in d and d['total_bytes_estimate']:
                    percent = (d['downloaded_bytes'] / d['total_bytes_estimate']) * 100
                else:
                    percent = 0
                
                # Get speed and ETA
                speed = d.get('speed', 0)
                eta = d.get('eta', 0)
                
                # Update progress
                progress_data = {
                    'percent': round(percent, 1),
                    'downloaded': d.get('downloaded_bytes', 0),
                    'total': d.get('total_bytes', d.get('total_bytes_estimate', 0)),
                    'speed': speed,
                    'eta': eta,
                    'status': 'downloading'
                }
                
                download_progress[self.download_id] = progress_data
                
                # Emit to specific download session
                self.socketio.emit('progress_update', progress_data, room=self.download_id)
                
            except Exception as e:
                print(f"Progress hook error: {e}")
                
        elif d['status'] == 'finished':
            progress_data = {
                'percent': 100,
                'status': 'finished',
                'filename': d.get('filename', '')
            }
            download_progress[self.download_id] = progress_data
            self.socketio.emit('download_complete', progress_data, room=self.download_id)

def download_video(url, download_id):
    """Download video using yt-dlp with progress tracking"""
    try:
        # Configure yt-dlp options
        ydl_opts = {
            'format': 'bestvideo+bestaudio/best',
            'merge_output_format': 'mp4',
            'outtmpl': f'{DOWNLOAD_DIR}/%(title)s.%(ext)s',
            'overwrites': True,
            'socket_timeout': 60,
            'retries': 10,
            'fragment_retries': 10,
            'progress_hooks': [ProgressHook(download_id, socketio)]
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            # Get video info first
            info = ydl.extract_info(url, download=False)
            title = info.get('title', 'Unknown')
            duration = info.get('duration', 0)
            uploader = info.get('uploader', 'Unknown')
            
            # Store video info
            download_progress[download_id].update({
                'title': title,
                'duration': duration,
                'uploader': uploader
            })
            
            # Start download
            ydl.download([url])
            
    except Exception as e:
        error_data = {
            'status': 'error',
            'error': str(e)
        }
        download_progress[download_id] = error_data
        socketio.emit('download_error', error_data, room=download_id)

@app.route('/')
def index():
    """Main page with video URL input"""
    return render_template('index.html')

@app.route('/download', methods=['POST'])
def start_download():
    """Start video download process"""
    data = request.get_json()
    url = data.get('url', '').strip()
    
    if not url:
        return jsonify({'error': 'URL is required'}), 400
    
    # Generate unique download ID
    download_id = str(uuid.uuid4())
    
    # Initialize progress
    download_progress[download_id] = {
        'percent': 0,
        'status': 'starting',
        'url': url,
        'created_at': datetime.now().isoformat()
    }
    
    # Start download in background thread
    thread = threading.Thread(target=download_video, args=(url, download_id))
    thread.daemon = True
    thread.start()
    
    return jsonify({'download_id': download_id})

@app.route('/progress/<download_id>')
def get_progress(download_id):
    """Get current download progress"""
    progress = download_progress.get(download_id, {'status': 'not_found'})
    return jsonify(progress)

@app.route('/download-page/<download_id>')
def download_page(download_id):
    """Download progress and completion page"""
    return render_template('download.html', download_id=download_id)

@app.route('/download-file/<download_id>')
def download_file(download_id):
    """Serve the downloaded file"""
    if download_id not in download_progress:
        return jsonify({'error': 'Download not found'}), 404

    progress = download_progress[download_id]
    if progress.get('status') != 'finished':
        return jsonify({'error': 'Download not completed'}), 400

    filename = progress.get('filename')
    if not filename or not os.path.exists(filename):
        return jsonify({'error': 'File not found'}), 404

    # Get the base filename for download
    base_filename = os.path.basename(filename)

    try:
        return send_file(
            filename,
            as_attachment=True,
            download_name=base_filename,
            mimetype='video/mp4'
        )
    except Exception as e:
        return jsonify({'error': f'Error serving file: {str(e)}'}), 500

@app.route('/api/supported-platforms')
def supported_platforms():
    """Get list of supported platforms"""
    platforms = [
        {'name': 'Instagram', 'icon': 'fab fa-instagram', 'domains': ['instagram.com']},
        {'name': 'Facebook', 'icon': 'fab fa-facebook', 'domains': ['facebook.com', 'fb.watch']},
        {'name': 'TikTok', 'icon': 'fab fa-tiktok', 'domains': ['tiktok.com']},
        {'name': 'YouTube', 'icon': 'fab fa-youtube', 'domains': ['youtube.com', 'youtu.be']},
        {'name': 'Twitter/X', 'icon': 'fab fa-twitter', 'domains': ['twitter.com', 'x.com']},
        {'name': 'Reddit', 'icon': 'fab fa-reddit', 'domains': ['reddit.com']},
        {'name': 'Vimeo', 'icon': 'fab fa-vimeo', 'domains': ['vimeo.com']},
        {'name': 'Dailymotion', 'icon': 'fas fa-video', 'domains': ['dailymotion.com']},
    ]
    return jsonify(platforms)

@socketio.on('join_download')
def on_join_download(data):
    """Join a specific download room for real-time updates"""
    download_id = data['download_id']
    session['download_id'] = download_id
    # Join the room for this specific download
    from flask_socketio import join_room
    join_room(download_id)

    # Send current progress if available
    if download_id in download_progress:
        emit('progress_update', download_progress[download_id])

@socketio.on('disconnect')
def on_disconnect():
    """Handle client disconnect"""
    print('Client disconnected')

if __name__ == '__main__':
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
