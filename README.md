# Universal Video Downloader

A modern, visually appealing web application for downloading videos from various social media platforms including Instagram, Facebook, TikTok, YouTube, and more.

## Features

- 🎨 **Modern UI Design**: Beautiful, responsive interface with gradient backgrounds and smooth animations
- 📱 **Multi-Platform Support**: Download from Instagram, Facebook, TikTok, YouTube, Twitter, and many more
- 📊 **Real-time Progress**: Live progress tracking with download speed and ETA
- 🚀 **Fast Downloads**: High-quality video downloads with optimal settings
- 💻 **Cross-Device**: Works on desktop, tablet, and mobile devices
- 🔒 **Safe & Secure**: No ads, no malware, completely safe to use

## Supported Platforms

- Instagram (Reels, Posts, Stories)
- Facebook (Videos, Reels)
- TikTok
- YouTube (Videos, Shorts)
- Twitter/X
- Reddit
- Vimeo
- Dailymotion
- And many more...

## Installation

### Option 1: Using UV Package Manager (Recommended)

1. **Install UV** (if not already installed):
   ```bash
   # macOS/Linux
   curl -LsSf https://astral.sh/uv/install.sh | sh

   # Windows (PowerShell)
   powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

   # Or using pip
   pip install uv

   # Or using Homebrew (macOS)
   brew install uv
   ```

2. **Clone and setup the project**:
   ```bash
   git clone <repository-url>
   cd download_instagram
   ```

3. **Run with UV** (automatically handles virtual environment and dependencies):
   ```bash
   python run_uv.py
   ```

   Or manually:
   ```bash
   uv sync          # Install dependencies
   uv run python app.py  # Run the application
   ```

### Option 2: Using Traditional pip

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd download_instagram
   ```

2. **Create virtual environment** (recommended):
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application**:
   ```bash
   python app.py
   ```

### Access the Application

Open your browser and navigate to:
```
http://localhost:8080
```

## Usage

1. **Paste Video URL**: Copy and paste the video URL from any supported platform
2. **Start Download**: Click the "Start Download" button
3. **Monitor Progress**: Watch real-time download progress with speed and ETA
4. **Download File**: Once complete, download the video file to your device

## Technical Details

- **Backend**: Flask with Socket.IO for real-time updates
- **Video Processing**: yt-dlp library (same as used in higher.py)
- **Frontend**: Bootstrap 5, Font Awesome, custom CSS with animations
- **Real-time Updates**: WebSocket connections for live progress tracking

## File Structure

```
download_instagram/
├── app.py                 # Main Flask application
├── higher.py             # Original script (untouched)
├── requirements.txt      # Python dependencies
├── templates/           # HTML templates
│   ├── base.html        # Base template
│   ├── index.html       # Home page
│   └── download.html    # Download progress page
├── static/             # Static assets
│   ├── css/
│   │   └── style.css   # Custom styles
│   └── js/
│       └── main.js     # JavaScript functionality
└── downloads/          # Downloaded videos (created automatically)
```

## Configuration

The application uses the same yt-dlp configuration as your original `higher.py` file:
- Best quality video + audio
- MP4 format output
- Automatic retries
- Extended timeouts

## Notes

- Downloads are stored in the `downloads/` directory
- The original `higher.py` file remains completely untouched
- All downloads are temporary and should be moved to permanent storage
- Large videos may take longer to process and download

## Browser Compatibility

- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers

## Troubleshooting

1. **Download fails**: Check if the URL is valid and the video is publicly accessible
2. **Slow downloads**: This depends on your internet connection and the source server
3. **Browser compatibility**: Use a modern browser with WebSocket support

## License

This project is for educational purposes. Please respect the terms of service of the platforms you're downloading from.
