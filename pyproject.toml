[project]
name = "universal-video-downloader"
version = "1.0.0"
description = "A modern web application for downloading videos from various social media platforms"
authors = [
    {name = "Video Downloader Team"}
]
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "eventlet==0.33.3",
    "flask==2.3.3",
    "flask-socketio==5.3.6",
    "python-engineio==4.7.1",
    "python-socketio==5.8.0",
    "werkzeug==2.3.7",
    "yt-dlp==2025.8.11",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]
exclude = [
    "downloads/",
    "reels_hq/",
    ".venv/",
    "__pycache__/",
    "*.pyc",
    ".git/",
    ".DS_Store"
]

[tool.black]
line-length = 88
target-version = ['py39']

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
