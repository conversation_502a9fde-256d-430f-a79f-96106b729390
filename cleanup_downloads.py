#!/usr/bin/env python3
"""
Cleanup script for corrupted or incomplete downloads
"""

import os
import glob

def cleanup_downloads():
    """Clean up corrupted download files"""
    download_dir = 'downloads'
    
    if not os.path.exists(download_dir):
        print("📁 Downloads directory doesn't exist")
        return
    
    # Patterns for corrupted files
    corrupted_patterns = [
        '*.fdash-*',  # DASH fragments
        '*.f*-*',     # Other fragment patterns
        '*.part',     # Incomplete downloads
        '*.ytdl',     # yt-dlp temporary files
        '*.temp',     # Temporary files
    ]
    
    cleaned_files = []
    
    for pattern in corrupted_patterns:
        files = glob.glob(os.path.join(download_dir, pattern))
        for file_path in files:
            try:
                os.remove(file_path)
                cleaned_files.append(os.path.basename(file_path))
                print(f"🗑️ Removed corrupted file: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"❌ Error removing {file_path}: {e}")
    
    if cleaned_files:
        print(f"\n✅ Cleaned up {len(cleaned_files)} corrupted files")
    else:
        print("✅ No corrupted files found")
    
    # List remaining files
    remaining_files = [f for f in os.listdir(download_dir) 
                      if os.path.isfile(os.path.join(download_dir, f))]
    
    if remaining_files:
        print(f"\n📁 Remaining files in downloads:")
        for file in remaining_files:
            file_path = os.path.join(download_dir, file)
            size = os.path.getsize(file_path)
            size_mb = size / (1024 * 1024)
            print(f"   📄 {file} ({size_mb:.1f} MB)")
    else:
        print("\n📁 Downloads directory is now empty")

def check_ffmpeg():
    """Check FFmpeg installation"""
    import shutil
    
    if shutil.which('ffmpeg'):
        print("✅ FFmpeg is installed and available")
        return True
    else:
        print("❌ FFmpeg is not installed")
        print("\n📦 To install FFmpeg:")
        print("   • macOS: brew install ffmpeg")
        print("   • Ubuntu/Debian: sudo apt install ffmpeg")
        print("   • Windows: Download from https://ffmpeg.org/download.html")
        print("   • Or use conda: conda install ffmpeg")
        return False

def main():
    print("🧹 Universal Video Downloader - Cleanup Tool")
    print("=" * 50)
    
    # Check FFmpeg
    print("\n🔍 Checking FFmpeg installation...")
    has_ffmpeg = check_ffmpeg()
    
    # Cleanup downloads
    print("\n🧹 Cleaning up corrupted downloads...")
    cleanup_downloads()
    
    if not has_ffmpeg:
        print("\n💡 Recommendation:")
        print("   Install FFmpeg for better video quality and proper file merging.")
        print("   After installing FFmpeg, restart the video downloader application.")

if __name__ == "__main__":
    main()
