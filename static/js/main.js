// Main JavaScript file for Universal Video Downloader

// Utility functions
const Utils = {
    // Format bytes to human readable format
    formatBytes: function(bytes, decimals = 1) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    },

    // Format seconds to human readable time
    formatTime: function(seconds) {
        if (!seconds || seconds === Infinity || isNaN(seconds)) return '--:--';
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },

    // Format duration from seconds (supports hours)
    formatDuration: function(seconds) {
        if (!seconds || isNaN(seconds)) return '--:--';
        const hours = Math.floor(seconds / 3600);
        const mins = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        
        if (hours > 0) {
            return `${hours}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    },

    // Validate URL
    isValidUrl: function(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    },

    // Detect platform from URL
    detectPlatform: function(url) {
        const platforms = {
            'instagram.com': { name: 'Instagram', icon: 'fab fa-instagram', color: '#E4405F' },
            'facebook.com': { name: 'Facebook', icon: 'fab fa-facebook', color: '#1877F2' },
            'tiktok.com': { name: 'TikTok', icon: 'fab fa-tiktok', color: '#000000' },
            'youtube.com': { name: 'YouTube', icon: 'fab fa-youtube', color: '#FF0000' },
            'youtu.be': { name: 'YouTube', icon: 'fab fa-youtube', color: '#FF0000' },
            'twitter.com': { name: 'Twitter', icon: 'fab fa-twitter', color: '#1DA1F2' },
            'x.com': { name: 'X (Twitter)', icon: 'fab fa-x-twitter', color: '#000000' }
        };

        try {
            const hostname = new URL(url).hostname.toLowerCase();
            for (const [domain, info] of Object.entries(platforms)) {
                if (hostname.includes(domain)) {
                    return info;
                }
            }
        } catch (e) {
            // Invalid URL
        }
        
        return { name: 'Unknown', icon: 'fas fa-video', color: '#6366f1' };
    },

    // Show notification
    showNotification: function(message, type = 'info', duration = 5000) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        `;
        
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto remove after duration
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, duration);
    },

    // Animate number counting
    animateNumber: function(element, start, end, duration = 1000) {
        const startTime = performance.now();
        const difference = end - start;
        
        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const current = start + (difference * easeOut);
            
            element.textContent = Math.round(current);
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }
        
        requestAnimationFrame(updateNumber);
    }
};

// URL Input Enhancement
class URLInputEnhancer {
    constructor(inputElement) {
        this.input = inputElement;
        this.init();
    }

    init() {
        // Add paste button functionality
        this.addPasteButton();
        
        // Add URL validation
        this.input.addEventListener('input', this.validateURL.bind(this));
        this.input.addEventListener('paste', this.handlePaste.bind(this));
        
        // Add platform detection
        this.input.addEventListener('blur', this.detectPlatform.bind(this));
    }

    addPasteButton() {
        const pasteBtn = this.input.parentElement.querySelector('.input-group-text');
        if (pasteBtn) {
            pasteBtn.style.cursor = 'pointer';
            pasteBtn.addEventListener('click', this.pasteFromClipboard.bind(this));
        }
    }

    async pasteFromClipboard() {
        try {
            const text = await navigator.clipboard.readText();
            if (text && Utils.isValidUrl(text)) {
                this.input.value = text;
                this.input.dispatchEvent(new Event('input'));
                Utils.showNotification('URL pasted successfully!', 'success', 2000);
            } else {
                Utils.showNotification('No valid URL found in clipboard', 'warning', 3000);
            }
        } catch (err) {
            // Fallback for browsers that don't support clipboard API
            this.input.focus();
            Utils.showNotification('Please paste the URL manually', 'info', 3000);
        }
    }

    validateURL() {
        const url = this.input.value.trim();
        
        if (url && !Utils.isValidUrl(url)) {
            this.input.classList.add('is-invalid');
            this.showValidationMessage('Please enter a valid URL', 'error');
        } else {
            this.input.classList.remove('is-invalid');
            this.hideValidationMessage();
        }
    }

    handlePaste(event) {
        setTimeout(() => {
            this.validateURL();
            this.detectPlatform();
        }, 100);
    }

    detectPlatform() {
        const url = this.input.value.trim();
        if (url && Utils.isValidUrl(url)) {
            const platform = Utils.detectPlatform(url);
            this.showPlatformInfo(platform);
        }
    }

    showPlatformInfo(platform) {
        // Remove existing platform info
        const existingInfo = document.querySelector('.platform-info');
        if (existingInfo) {
            existingInfo.remove();
        }

        // Create platform info element
        const platformInfo = document.createElement('div');
        platformInfo.className = 'platform-info mt-2';
        platformInfo.innerHTML = `
            <small class="text-muted">
                <i class="${platform.icon}" style="color: ${platform.color}"></i>
                Detected platform: <strong>${platform.name}</strong>
            </small>
        `;

        this.input.parentElement.parentElement.appendChild(platformInfo);
    }

    showValidationMessage(message, type) {
        this.hideValidationMessage();
        
        const validationDiv = document.createElement('div');
        validationDiv.className = `invalid-feedback d-block`;
        validationDiv.textContent = message;
        
        this.input.parentElement.parentElement.appendChild(validationDiv);
    }

    hideValidationMessage() {
        const existingFeedback = this.input.parentElement.parentElement.querySelector('.invalid-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }
    }
}

// Progress Animation
class ProgressAnimator {
    constructor() {
        this.animationFrame = null;
    }

    animateProgressBar(progressBar, targetPercent, duration = 500) {
        const startPercent = parseFloat(progressBar.style.width) || 0;
        const difference = targetPercent - startPercent;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function
            const easeOut = 1 - Math.pow(1 - progress, 2);
            const currentPercent = startPercent + (difference * easeOut);
            
            progressBar.style.width = currentPercent + '%';
            progressBar.setAttribute('aria-valuenow', currentPercent);
            
            if (progress < 1) {
                this.animationFrame = requestAnimationFrame(animate);
            }
        };

        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
        
        requestAnimationFrame(animate);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize URL input enhancer
    const urlInput = document.getElementById('videoUrl');
    if (urlInput) {
        new URLInputEnhancer(urlInput);
    }

    // Add smooth scrolling to all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add loading animation to buttons
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (this.type === 'submit' || this.classList.contains('download-btn')) {
                // Add ripple effect
                const ripple = document.createElement('span');
                ripple.className = 'ripple';
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            }
        });
    });

    // Add intersection observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationDelay = '0s';
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.feature-card, .platform-card').forEach(el => {
        observer.observe(el);
    });
});

// Export utilities for use in other scripts
window.VideoDownloaderUtils = Utils;
window.ProgressAnimator = ProgressAnimator;
