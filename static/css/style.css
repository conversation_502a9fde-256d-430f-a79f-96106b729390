/* Custom CSS Variables */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #ec4899;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #3b82f6;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Global Styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    line-height: 1.6;
}

.main-content {
    min-height: calc(100vh - 200px);
    padding: 2rem 0;
}

/* Navigation */
.bg-gradient-primary {
    background: var(--gradient-primary) !important;
    box-shadow: var(--shadow-md);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

/* Hero Section */
.hero-section {
    padding: 4rem 0 2rem;
    text-align: center;
}

.hero-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 3rem 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-xl);
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: white;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    font-weight: 300;
}

/* Download Section */
.download-section {
    padding: 2rem 0;
}

.download-card {
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow-xl);
    border: none;
    overflow: hidden;
    position: relative;
}

.download-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.download-btn {
    background: var(--gradient-primary);
    border: none;
    border-radius: 15px;
    padding: 1rem 2rem;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.download-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.download-btn:active {
    transform: translateY(0);
}

.download-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.download-btn:hover::before {
    left: 100%;
}

/* Features Section */
.features-section {
    padding: 4rem 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin: 2rem 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.feature-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    height: 100%;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

/* Platforms Section */
.platforms-section {
    padding: 4rem 0;
}

.section-subtitle {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 3rem;
}

.platform-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.platform-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.platform-card i {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.platform-card span {
    font-weight: 600;
    color: var(--dark-color);
}

/* Progress Page Styles */
.download-progress-section {
    padding: 2rem 0;
}

.progress-card {
    background: white;
    border-radius: 20px;
    box-shadow: var(--shadow-xl);
    border: none;
    overflow: hidden;
    position: relative;
}

.progress-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.video-info {
    background: var(--light-color);
    border-radius: 15px;
    padding: 1.5rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
}

.status-badge.status-downloading {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.status-badge.status-complete {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.status-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.progress-lg {
    height: 20px;
    border-radius: 10px;
    background: var(--light-color);
    overflow: hidden;
}

.progress-lg .progress-bar {
    background: var(--gradient-primary);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.download-stats {
    background: var(--light-color);
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.stat-item {
    padding: 1rem;
}

.stat-item i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--dark-color);
}

.stat-label {
    font-size: 0.9rem;
    color: #6b7280;
    font-weight: 500;
}

.success-icon i,
.error-icon i {
    font-size: 4rem;
}

.tips-section {
    padding: 2rem 0;
}

.tips-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.tips-card h5 {
    color: white;
    font-weight: 600;
}

.tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tips-list li {
    color: rgba(255, 255, 255, 0.9);
    padding: 0.5rem 0;
    font-weight: 400;
}

/* Footer */
.footer {
    margin-top: auto;
}

.platform-icons {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.platform-icon {
    font-size: 2rem;
    color: #6b7280;
    transition: all 0.3s ease;
    cursor: pointer;
}

.platform-icon:hover {
    color: var(--primary-color);
    transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .hero-content {
        padding: 2rem 1rem;
    }
    
    .platform-icons {
        gap: 1rem;
    }
    
    .platform-icon {
        font-size: 1.5rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.feature-card,
.platform-card,
.download-card,
.progress-card {
    animation: fadeInUp 0.6s ease-out;
}

.animate-in {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading shimmer effect */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Pulse animation for download button */
.download-btn.loading {
    animation: pulse 2s infinite;
}

/* Ripple effect */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}
