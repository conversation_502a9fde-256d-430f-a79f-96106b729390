#!/usr/bin/env python3
"""
Universal Video Downloader - UV Package Manager Startup Script
"""

import os
import sys
import subprocess
import shutil

def check_uv_installed():
    """Check if uv is installed"""
    if shutil.which("uv") is None:
        print("❌ UV package manager is not installed")
        print("📦 Install UV with one of these methods:")
        print("   • macOS/Linux: curl -LsSf https://astral.sh/uv/install.sh | sh")
        print("   • Windows: powershell -c \"irm https://astral.sh/uv/install.ps1 | iex\"")
        print("   • pip: pip install uv")
        print("   • Homebrew: brew install uv")
        return False
    print("✅ UV package manager is installed")
    return True

def setup_virtual_environment():
    """Set up virtual environment with uv"""
    print("🔧 Setting up virtual environment...")
    
    # Create virtual environment if it doesn't exist
    if not os.path.exists(".venv"):
        result = subprocess.run(["uv", "venv"], capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ Failed to create virtual environment: {result.stderr}")
            return False
        print("📁 Created virtual environment: .venv")
    else:
        print("📁 Virtual environment already exists: .venv")
    
    return True

def install_dependencies():
    """Install dependencies using uv"""
    print("📦 Installing dependencies...")
    
    result = subprocess.run(["uv", "sync"], capture_output=True, text=True)
    if result.returncode != 0:
        print(f"❌ Failed to install dependencies: {result.stderr}")
        return False
    
    print("✅ Dependencies installed successfully")
    return True

def create_directories():
    """Create necessary directories"""
    directories = ['downloads', 'static/css', 'static/js', 'templates']
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Created directory: {directory}")

def start_application():
    """Start the application using uv run"""
    print("\n🌐 Starting web server with UV...")
    print("📱 Open your browser and go to: http://localhost:5000")
    print("⏹️  Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Use uv run to execute the application
        subprocess.run(["uv", "run", "python", "app.py"])
    except KeyboardInterrupt:
        print("\n👋 Server stopped. Goodbye!")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

def main():
    """Main startup function"""
    print("🚀 Starting Universal Video Downloader with UV...")
    print("=" * 50)
    
    # Check if uv is installed
    if not check_uv_installed():
        sys.exit(1)
    
    # Setup virtual environment
    if not setup_virtual_environment():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Start the application
    start_application()

if __name__ == "__main__":
    main()
